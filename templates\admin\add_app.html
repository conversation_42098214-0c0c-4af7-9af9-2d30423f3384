{% extends "base.html" %}

{% block title %}Add New App - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-plus-circle"></i> Add New App</h1>
        <a href="{{ url_for('admin_apps') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Apps
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <!-- Basic Information -->
                        <h5 class="mb-3">Basic Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">App Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="developer" class="form-label">Developer *</label>
                                <input type="text" class="form-control" id="developer" name="developer" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description *</label>
                            <input type="text" class="form-control" id="short_description" name="short_description"
                                   maxlength="200" required>
                            <div class="form-text">Brief description shown in app listings (max 200 characters)</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="6" required></textarea>
                            <div class="form-text">Detailed description shown on app page</div>
                        </div>

                        <!-- App Details -->
                        <h5 class="mb-3 mt-4">App Details</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="version" class="form-label">Version *</label>
                                <input type="text" class="form-control" id="version" name="version"
                                       placeholder="1.0.0" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="Productivity">Productivity</option>
                                    <option value="Games">Games</option>
                                    <option value="Education">Education</option>
                                    <option value="Business">Business</option>
                                    <option value="Entertainment">Entertainment</option>
                                    <option value="Utilities">Utilities</option>
                                    <option value="Graphics">Graphics</option>
                                    <option value="Development">Development</option>
                                    <option value="Social">Social</option>
                                    <option value="Music">Music</option>
                                    <option value="Video">Video</option>
                                    <option value="Security">Security</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="price" name="price"
                                       min="0" step="0.01" value="0">
                                <div class="form-text">Set to 0 for free apps</div>
                            </div>
                        </div>

                        <!-- Files -->
                        <h5 class="mb-3 mt-4">Download Options</h5>
                        <div class="mb-3">
                            <label class="form-label">Download Method *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="download_method" id="method_file" value="file" checked>
                                <label class="form-check-label" for="method_file">
                                    Upload File
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="download_method" id="method_url" value="url">
                                <label class="form-check-label" for="method_url">
                                    External Download Link
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="file_upload_section">
                            <label for="app_file" class="form-label">App File</label>
                            <input type="file" class="form-control" id="app_file" name="app_file"
                                   accept=".zip,.exe,.dmg,.deb,.rpm,.apk,.rar,.7z">
                            <div class="form-text">Supported formats: ZIP, EXE, DMG, DEB, RPM, APK, RAR, 7Z (max 100MB)</div>
                        </div>

                        <div class="mb-3" id="external_url_section" style="display: none;">
                            <label for="external_url" class="form-label">External Download URL</label>
                            <input type="url" class="form-control" id="external_url" name="external_url"
                                   placeholder="https://example.com/download/app.zip">
                            <div class="form-text">Direct link to download the app from another website</div>
                        </div>

                        <div class="mb-3">
                            <label for="icon" class="form-label">App Icon</label>
                            <input type="file" class="form-control" id="icon" name="icon"
                                   accept=".png,.jpg,.jpeg,.gif">
                            <div class="form-text">Recommended size: 512x512px (PNG, JPG, GIF)</div>
                        </div>

                        <div class="mb-3">
                            <label for="screenshots" class="form-label">Screenshots</label>
                            <input type="file" class="form-control" id="screenshots" name="screenshots"
                                   accept=".png,.jpg,.jpeg,.gif" multiple>
                            <div class="form-text">Upload multiple screenshots to showcase your app</div>
                        </div>

                        <!-- Options -->
                        <h5 class="mb-3 mt-4">Options</h5>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured">
                                <label class="form-check-label" for="is_featured">
                                    Featured App
                                </label>
                                <div class="form-text">Featured apps appear in the featured section</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-plus-circle"></i> Add App
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Guidelines</h5>
                </div>
                <div class="card-body">
                    <h6>App File Requirements:</h6>
                    <ul class="small">
                        <li>Maximum file size: 100MB</li>
                        <li>Supported formats: ZIP, EXE, DMG, DEB, RPM, APK</li>
                        <li>Ensure your app is virus-free</li>
                    </ul>

                    <h6>Icon Guidelines:</h6>
                    <ul class="small">
                        <li>Recommended size: 512x512px</li>
                        <li>Use PNG format for best quality</li>
                        <li>Keep it simple and recognizable</li>
                    </ul>

                    <h6>Screenshots:</h6>
                    <ul class="small">
                        <li>Show key features of your app</li>
                        <li>Use high-quality images</li>
                        <li>Maximum 10 screenshots</li>
                    </ul>

                    <h6>Description Tips:</h6>
                    <ul class="small">
                        <li>Be clear and concise</li>
                        <li>Highlight key features</li>
                        <li>Include system requirements</li>
                        <li>Mention any special permissions needed</li>
                    </ul>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list-check"></i> Checklist</h5>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check1">
                        <label class="form-check-label small" for="check1">
                            App file is tested and working
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check2">
                        <label class="form-check-label small" for="check2">
                            Description is complete and accurate
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check3">
                        <label class="form-check-label small" for="check3">
                            Icon and screenshots are high quality
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check4">
                        <label class="form-check-label small" for="check4">
                            Category and pricing are correct
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Download method toggle
document.querySelectorAll('input[name="download_method"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const fileSection = document.getElementById('file_upload_section');
        const urlSection = document.getElementById('external_url_section');
        const appFileInput = document.getElementById('app_file');
        const externalUrlInput = document.getElementById('external_url');

        if (this.value === 'file') {
            fileSection.style.display = 'block';
            urlSection.style.display = 'none';
            appFileInput.required = true;
            externalUrlInput.required = false;
            externalUrlInput.value = '';
        } else {
            fileSection.style.display = 'none';
            urlSection.style.display = 'block';
            appFileInput.required = false;
            externalUrlInput.required = true;
            appFileInput.value = '';
        }
    });
});

// File size validation
document.getElementById('app_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file && file.size > 100 * 1024 * 1024) { // 100MB
        alert('File size must be less than 100MB');
        e.target.value = '';
    }
});

// Character counter for short description
document.getElementById('short_description').addEventListener('input', function(e) {
    const maxLength = 200;
    const currentLength = e.target.value.length;
    const remaining = maxLength - currentLength;

    let helpText = e.target.nextElementSibling;
    helpText.textContent = `Brief description shown in app listings (${remaining} characters remaining)`;

    if (remaining < 20) {
        helpText.classList.add('text-warning');
    } else {
        helpText.classList.remove('text-warning');
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const downloadMethod = document.querySelector('input[name="download_method"]:checked').value;
    const appFile = document.getElementById('app_file').files[0];
    const externalUrl = document.getElementById('external_url').value;

    if (downloadMethod === 'file' && !appFile) {
        e.preventDefault();
        alert('Please select a file to upload or choose external URL option.');
        return false;
    }

    if (downloadMethod === 'url' && !externalUrl) {
        e.preventDefault();
        alert('Please enter an external download URL or choose file upload option.');
        return false;
    }
});
</script>
{% endblock %}
