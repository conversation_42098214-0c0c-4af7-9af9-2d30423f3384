# 🏪 App Store Website

A full-featured App Store website built with Python Flask, featuring a responsive design, admin panel, and comprehensive app management system.

## ✨ Features

### 🔧 Core Functionality
- **Browse & Download Apps**: Users can browse apps by category, search, and download
- **Detailed App Pages**: Each app has a dedicated page with description, screenshots, and download link
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Dark/Light Theme**: Automatic theme detection with manual toggle option

### 👨‍💼 Admin Panel
- **User-friendly Dashboard**: Overview of apps, downloads, and recent activity
- **App Management**: Add, edit, and remove apps with file uploads
- **File Handling**: Automatic file size validation and image resizing
- **Secure Access**: Protected admin area with login system

### 🔒 Security & Logging
- **Input Sanitization**: All user inputs are sanitized to prevent XSS attacks
- **File Validation**: Strict file type and size validation
- **Comprehensive Logging**: Track downloads, admin actions, and system events
- **Session Management**: Secure session handling with timeouts

### 📱 Technical Features
- **SQLite Database**: Lightweight database for app metadata
- **File Upload System**: Handle app files, icons, and screenshots
- **Image Processing**: Automatic image resizing and optimization
- **Pagination**: Efficient browsing of large app collections
- **Search & Filter**: Advanced search and category filtering

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project files**
2. **Run the setup script**:
   ```bash
   py -3.11 setup.py
   ```
   This will:
   - Install all required dependencies
   - Create necessary directories
   - Initialize the database
   - Optionally create sample data

3. **Start the application**:
   ```bash
   py -3.11 run.py
   ```

4. **Open your browser** and go to: `http://localhost:5000`

### Default Admin Credentials
- **Username**: `admin`
- **Password**: `admin123`

⚠️ **Important**: Change these credentials in production!

## 📁 Project Structure

```
app-store/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── config.py              # Configuration settings
├── run.py                 # Application runner
├── setup.py               # Setup script
├── requirements.txt       # Python dependencies
├── README.md              # This file
├── templates/             # HTML templates
│   ├── base.html         # Base template with theme support
│   ├── index.html        # Main store page
│   ├── app_detail.html   # App detail page
│   └── admin/            # Admin templates
│       ├── login.html
│       ├── dashboard.html
│       ├── apps.html
│       ├── add_app.html
│       └── edit_app.html
├── static/               # Static files
│   ├── css/
│   │   └── style.css    # Custom styles with theme support
│   └── js/
│       └── app.js       # JavaScript functionality
├── uploads/              # Uploaded files (created automatically)
│   ├── apps/            # App files
│   └── screenshots/     # Icons and screenshots
└── logs/                 # Log files (created automatically)
```

## 🎯 Usage Guide

### For End Users

1. **Browse Apps**: Visit the homepage to see featured and all apps
2. **Search**: Use the search bar to find specific apps
3. **Filter**: Select categories from the dropdown to filter apps
4. **View Details**: Click on any app to see detailed information
5. **Download**: Click the download button on app detail pages
6. **Theme Toggle**: Use the sun/moon icon to switch between light and dark themes

### For Administrators

1. **Login**: Click "Admin" in the navigation and login with credentials
2. **Dashboard**: View statistics and recent activity
3. **Add Apps**: Use "Add New App" to upload new applications
4. **Manage Apps**: Edit or delete existing apps from the apps management page
5. **File Management**: Upload app files, icons, and screenshots

### Adding New Apps

When adding a new app, you can upload:
- **App File**: The actual application (ZIP, EXE, DMG, DEB, RPM, APK)
- **Icon**: App icon (PNG, JPG, GIF) - automatically resized to 512x512
- **Screenshots**: Multiple screenshots to showcase the app

## 🔧 Configuration

### Environment Variables
- `SECRET_KEY`: Flask secret key for sessions (change in production)
- `DATABASE_URL`: Database connection string (defaults to SQLite)
- `ADMIN_USERNAME`: Admin username (default: admin)
- `ADMIN_PASSWORD`: Admin password (default: admin123)

### File Limits
- Maximum file size: 100MB
- Supported app formats: ZIP, EXE, DMG, DEB, RPM, APK
- Supported image formats: PNG, JPG, JPEG, GIF

## 🛡️ Security Features

- **Input Sanitization**: All user inputs are cleaned using bleach
- **File Validation**: Strict file type and size checking
- **Secure File Names**: Files are renamed with UUIDs to prevent conflicts
- **Session Security**: Secure session management with timeouts
- **Admin Protection**: Admin routes require authentication
- **XSS Prevention**: Template auto-escaping and input sanitization

## 📊 Logging

The application logs the following events:
- App downloads (IP address, timestamp, user agent)
- Admin actions (login, logout, add/edit/delete apps)
- System errors and warnings
- File upload activities

Logs are stored in the `logs/` directory and also output to console.

## 🎨 Themes

The application supports both light and dark themes:
- **Automatic Detection**: Detects system preference on first visit
- **Manual Toggle**: Users can switch themes using the toggle button
- **Persistent**: Theme preference is saved in browser storage
- **Responsive**: All components adapt to the selected theme

## 🔄 API Endpoints

### Public Endpoints
- `GET /` - Main store page
- `GET /app/<id>` - App detail page
- `GET /download/<id>` - Download app file
- `POST /toggle-theme` - Toggle theme preference

### Admin Endpoints
- `GET /admin/login` - Admin login page
- `GET /admin` - Admin dashboard
- `GET /admin/apps` - Manage apps
- `GET /admin/apps/add` - Add new app
- `POST /admin/apps/add` - Submit new app
- `GET /admin/apps/edit/<id>` - Edit app
- `POST /admin/apps/edit/<id>` - Update app
- `POST /admin/apps/delete/<id>` - Delete app

## 🚀 Production Deployment

For production deployment:

1. **Change Security Settings**:
   ```bash
   export SECRET_KEY="your-secure-secret-key"
   export ADMIN_USERNAME="your-admin-username"
   export ADMIN_PASSWORD="your-secure-password"
   ```

2. **Use Production WSGI Server**:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

3. **Set Up Reverse Proxy** (nginx example):
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

4. **Database**: Consider using PostgreSQL or MySQL for production
5. **File Storage**: Consider using cloud storage for uploaded files
6. **SSL**: Set up HTTPS with Let's Encrypt or similar

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in `run.py` or kill the process using port 5000
2. **Permission errors**: Ensure the application has write permissions for uploads and logs directories
3. **File upload fails**: Check file size limits and available disk space
4. **Database errors**: Delete `app_store.db` and run setup again

### Debug Mode

To run in debug mode:
```bash
py -3.11 run.py --debug
```

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and enhancement requests.

## 📞 Support

If you encounter any issues or need help with setup, please check the troubleshooting section or create an issue in the project repository.
