from flask import Flask, render_template, request, redirect, url_for, flash, session, send_file, jsonify, abort
from flask_sqlalchemy import SQLAlchemy
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import os
import logging
from datetime import datetime
import bleach
from PIL import Image
import uuid
import re
from urllib.parse import urlparse

from config import Config
from models import db, App, Screenshot, DownloadLog, AdminLog, LoginLog, ActivityLog, User, AppRating

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Initialize extensions
    db.init_app(app)
    Config.init_app(app)

    # Setup logging
    setup_logging(app)

    with app.app_context():
        db.create_all()

    return app

def setup_logging(app):
    """Setup application logging"""
    if not os.path.exists(app.config['LOG_FOLDER']):
        os.makedirs(app.config['LOG_FOLDER'])

    log_file = os.path.join(app.config['LOG_FOLDER'], app.config['LOG_FILE'])

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

app = create_app()

def allowed_file(filename, file_type='app'):
    """Check if file extension is allowed"""
    if '.' not in filename:
        return False

    ext = filename.rsplit('.', 1)[1].lower()

    if file_type == 'image':
        return ext in {'png', 'jpg', 'jpeg', 'gif'}
    elif file_type == 'app':
        return ext in {'zip', 'exe', 'rar', '7z'}

    return ext in app.config['ALLOWED_EXTENSIONS']

def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    return bleach.clean(text, tags=[], strip=True)

def validate_url(url):
    """Validate external URL"""
    if not url:
        return False
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
    except:
        return False

def get_current_user():
    """Get current logged in user"""
    user_id = session.get('user_id')
    if user_id:
        return User.query.get(user_id)
    return None

def login_required(f):
    """Decorator to require login"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """Decorator to require admin role"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user or not user.is_admin():
            flash('Admin access required', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def publisher_required(f):
    """Decorator to require publisher or admin role"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user or not user.is_publisher():
            flash('Publisher access required', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def log_admin_action(action, details=None):
    """Log admin actions"""
    try:
        admin_log = AdminLog(
            action=action,
            details=details,
            ip_address=request.remote_addr
        )
        db.session.add(admin_log)
        db.session.commit()
        app.logger.info(f"Admin action: {action} - {details}")
    except Exception as e:
        app.logger.error(f"Failed to log admin action: {e}")

def log_download(app_id):
    """Log app download"""
    try:
        download_log = DownloadLog(
            app_id=app_id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(download_log)
        db.session.commit()
        app.logger.info(f"Download logged for app ID: {app_id}")
    except Exception as e:
        app.logger.error(f"Failed to log download: {e}")

def log_user_action(user_id, action, details=None):
    """Log user actions comprehensively"""
    try:
        user = User.query.get(user_id) if user_id else None
        username = user.username if user else 'Anonymous'

        # Log to ActivityLog
        activity_log = ActivityLog(
            user_id=user_id,
            username=username,
            action=action,
            details=details,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)

        # Also log login/logout to LoginLog
        if action in ['LOGIN', 'LOGOUT']:
            login_log = LoginLog(
                user_id=user_id,
                username=username,
                success=True,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(login_log)

        db.session.commit()
        app.logger.info(f"User action logged: {username} - {action} - {details}")
    except Exception as e:
        app.logger.error(f"Failed to log user action: {e}")

def log_failed_login(username, reason):
    """Log failed login attempts"""
    try:
        login_log = LoginLog(
            user_id=None,
            username=username,
            success=False,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            failure_reason=reason
        )
        db.session.add(login_log)
        db.session.commit()
        app.logger.warning(f"Failed login: {username} - {reason} from {request.remote_addr}")
    except Exception as e:
        app.logger.error(f"Failed to log failed login: {e}")

def log_app_view(app_id, user_id=None):
    """Log when someone views an app"""
    try:
        user = User.query.get(user_id) if user_id else None
        username = user.username if user else 'Anonymous'

        activity_log = ActivityLog(
            user_id=user_id,
            username=username,
            action='VIEW_APP',
            target_type='app',
            target_id=app_id,
            details=f'Viewed app ID: {app_id}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()
    except Exception as e:
        app.logger.error(f"Failed to log app view: {e}")

@app.route('/')
def index():
    """Main app store page"""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    search = request.args.get('search', '')

    query = App.query

    if category:
        query = query.filter(App.category == category)

    if search:
        search_term = f"%{search}%"
        query = query.filter(
            db.or_(
                App.name.like(search_term),
                App.description.like(search_term),
                App.developer.like(search_term)
            )
        )

    apps = query.order_by(App.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False
    )

    featured_apps = App.query.filter(App.is_featured == True).limit(6).all()
    categories = db.session.query(App.category).distinct().all()
    categories = [cat[0] for cat in categories]

    return render_template('index.html',
                         apps=apps,
                         featured_apps=featured_apps,
                         categories=categories,
                         current_category=category,
                         search_term=search)

@app.route('/app/<int:app_id>')
def app_detail(app_id):
    """App detail page"""
    app_item = App.query.get_or_404(app_id)
    related_apps = App.query.filter(
        App.category == app_item.category,
        App.id != app_id
    ).limit(4).all()

    # Log app view
    user_id = session.get('user_id')
    log_app_view(app_id, user_id)

    return render_template('app_detail.html',
                         app=app_item,
                         related_apps=related_apps)

@app.route('/download/<int:app_id>')
def download_app(app_id):
    """Download app file or redirect to external URL"""
    app_item = App.query.get_or_404(app_id)

    # Check if it's a paid app and handle payment logic here
    if app_item.price > 0:
        # For now, just show a message - you can integrate payment later
        flash(f'This is a paid app (${app_item.price:.2f}). Payment integration coming soon!', 'info')
        return redirect(url_for('app_detail', app_id=app_id))

    # Log download
    log_download(app_id)

    # Log user download action
    user_id = session.get('user_id')
    if user_id:
        log_user_action(user_id, 'DOWNLOAD_APP', f'Downloaded app: {app_item.name} (ID: {app_id})')
    else:
        log_user_action(None, 'DOWNLOAD_APP', f'Anonymous download: {app_item.name} (ID: {app_id})')

    # Increment download counter
    app_item.increment_downloads()

    # Handle external URL
    if app_item.external_url:
        return redirect(app_item.external_url)

    # Handle local file
    if app_item.file_path and os.path.exists(app_item.file_path):
        return send_file(app_item.file_path, as_attachment=True)

    flash('Download not available', 'error')
    return redirect(url_for('app_detail', app_id=app_id))

@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            app.logger.warning(f"File not found: {file_path}")
            abort(404)
    except Exception as e:
        app.logger.error(f"Error serving file {filename}: {e}")
        abort(404)

# Authentication routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            # Update last login
            user.last_login = datetime.utcnow()
            db.session.commit()

            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            session.permanent = True

            log_user_action(user.id, 'LOGIN', f'{user.role.title()} {user.username} logged in from {request.remote_addr}')
            flash(f'Welcome back, {user.username}!', 'success')

            # Redirect based on role
            if user.is_admin():
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('publisher_dashboard'))
        else:
            # Determine failure reason
            user_exists = User.query.filter_by(username=username).first()
            if not user_exists:
                reason = 'User not found'
            elif not user_exists.is_active:
                reason = 'Account disabled'
            else:
                reason = 'Invalid password'

            log_failed_login(username, reason)
            flash('Invalid credentials', 'error')

    return render_template('auth/login.html')

@app.route('/logout')
def logout():
    """User logout"""
    user_id = session.get('user_id')
    username = session.get('username', 'Unknown')
    role = session.get('role', 'user')

    if user_id:
        log_user_action(user_id, 'LOGOUT', f'{role.title()} {username} logged out')

    session.clear()
    flash('Logged out successfully', 'success')
    return redirect(url_for('index'))

@app.route('/admin/users')
@admin_required
def admin_users():
    """Admin user management"""
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('admin/users.html', users=users)

@app.route('/admin/users/add', methods=['GET', 'POST'])
@admin_required
def admin_add_user():
    """Admin create new user"""
    if request.method == 'POST':
        try:
            # Get form data and sanitize
            username = sanitize_input(request.form.get('username'))
            email = sanitize_input(request.form.get('email'))
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')
            role = sanitize_input(request.form.get('role'))

            # Validation
            if not username or len(username) < 3:
                flash('Username must be at least 3 characters long', 'error')
                return render_template('admin/add_user.html')

            if not email or '@' not in email:
                flash('Please enter a valid email address', 'error')
                return render_template('admin/add_user.html')

            if not password or len(password) < 6:
                flash('Password must be at least 6 characters long', 'error')
                return render_template('admin/add_user.html')

            if password != confirm_password:
                flash('Passwords do not match', 'error')
                return render_template('admin/add_user.html')

            if role not in ['admin', 'publisher']:
                flash('Invalid role selected', 'error')
                return render_template('admin/add_user.html')

            # Check if username or email already exists
            if User.query.filter_by(username=username).first():
                flash('Username already exists', 'error')
                return render_template('admin/add_user.html')

            if User.query.filter_by(email=email).first():
                flash('Email already registered', 'error')
                return render_template('admin/add_user.html')

            # Create new user
            new_user = User(
                username=username,
                email=email,
                role=role
            )
            new_user.set_password(password)

            db.session.add(new_user)
            db.session.commit()

            current_admin = get_current_user()
            log_admin_action('Create User', f'Admin {current_admin.username} created {role} account: {username}')
            flash(f'{role.title()} account created successfully!', 'success')
            return redirect(url_for('admin_users'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"User creation error: {e}")
            flash('Error creating account. Please try again.', 'error')

    return render_template('admin/add_user.html')

@app.route('/admin/users/edit/<int:user_id>', methods=['GET', 'POST'])
@admin_required
def admin_edit_user(user_id):
    """Admin edit user"""
    user_to_edit = User.query.get_or_404(user_id)
    current_admin = get_current_user()

    # Prevent admin from editing themselves through this interface
    if user_to_edit.id == current_admin.id:
        flash('You cannot edit your own account through this interface', 'warning')
        return redirect(url_for('admin_users'))

    if request.method == 'POST':
        try:
            # Get form data and sanitize
            username = sanitize_input(request.form.get('username'))
            email = sanitize_input(request.form.get('email'))
            password = request.form.get('password')
            role = sanitize_input(request.form.get('role'))

            # Validation
            if not username or len(username) < 3:
                flash('Username must be at least 3 characters long', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)

            if not email or '@' not in email:
                flash('Please enter a valid email address', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)

            if role not in ['admin', 'publisher']:
                flash('Invalid role selected', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)

            # Check if username or email already exists (excluding current user)
            existing_user = User.query.filter_by(username=username).first()
            if existing_user and existing_user.id != user_to_edit.id:
                flash('Username already exists', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)

            existing_email = User.query.filter_by(email=email).first()
            if existing_email and existing_email.id != user_to_edit.id:
                flash('Email already registered', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)

            # Update user
            user_to_edit.username = username
            user_to_edit.email = email
            user_to_edit.role = role

            # Update password if provided
            if password and len(password) >= 6:
                user_to_edit.set_password(password)

            db.session.commit()

            log_admin_action('Edit User', f'Admin {current_admin.username} edited user: {username}')
            flash(f'User {username} updated successfully!', 'success')
            return redirect(url_for('admin_users'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"User edit error: {e}")
            flash('Error updating user. Please try again.', 'error')

    return render_template('admin/edit_user.html', user=user_to_edit)

@app.route('/admin/users/delete/<int:user_id>', methods=['POST'])
@admin_required
def admin_delete_user(user_id):
    """Admin delete user"""
    user_to_delete = User.query.get_or_404(user_id)
    current_admin = get_current_user()

    # Prevent admin from deleting themselves
    if user_to_delete.id == current_admin.id:
        flash('You cannot delete your own account', 'error')
        return redirect(url_for('admin_users'))

    try:
        username = user_to_delete.username

        # Delete user's apps and associated files
        user_apps = App.query.filter_by(user_id=user_id).all()
        for app in user_apps:
            # Delete app files
            if app.file_path and os.path.exists(app.file_path):
                os.remove(app.file_path)
            if app.icon_path and os.path.exists(app.icon_path):
                os.remove(app.icon_path)

            # Delete screenshots
            for screenshot in app.screenshots:
                if os.path.exists(screenshot.file_path):
                    os.remove(screenshot.file_path)

        # Delete user (cascade will handle apps and related data)
        db.session.delete(user_to_delete)
        db.session.commit()

        log_admin_action('Delete User', f'Admin {current_admin.username} deleted user: {username}')
        flash(f'User {username} and all associated data deleted successfully', 'success')

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting user: {e}")
        flash('Error deleting user', 'error')

    return redirect(url_for('admin_users'))

@app.route('/app/<int:app_id>/rate', methods=['POST'])
def rate_app(app_id):
    """Rate an app"""
    app_item = App.query.get_or_404(app_id)

    try:
        rating_value = int(request.form.get('rating', 0))
        review_text = request.form.get('review', '').strip()

        if rating_value < 1 or rating_value > 5:
            flash('Rating must be between 1 and 5 stars', 'error')
            return redirect(url_for('app_detail', app_id=app_id))

        user_id = session.get('user_id')
        ip_address = request.remote_addr

        # Check if user/IP already rated this app
        existing_rating = None
        if user_id:
            existing_rating = AppRating.query.filter_by(app_id=app_id, user_id=user_id).first()
        else:
            existing_rating = AppRating.query.filter_by(app_id=app_id, ip_address=ip_address, user_id=None).first()

        if existing_rating:
            # Update existing rating
            existing_rating.rating = rating_value
            existing_rating.review = review_text
            existing_rating.timestamp = datetime.utcnow()
            action_msg = 'updated'
        else:
            # Create new rating
            new_rating = AppRating(
                app_id=app_id,
                user_id=user_id,
                rating=rating_value,
                review=review_text,
                ip_address=ip_address
            )
            db.session.add(new_rating)
            action_msg = 'added'

        db.session.commit()

        # Log the rating action
        if user_id:
            log_user_action(user_id, 'RATE_APP', f'Rated app: {app_item.name} - {rating_value} stars')
        else:
            log_user_action(None, 'RATE_APP', f'Anonymous rating: {app_item.name} - {rating_value} stars')

        flash(f'Rating {action_msg} successfully!', 'success')

    except ValueError:
        flash('Invalid rating value', 'error')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error rating app: {e}")
        flash('Error submitting rating', 'error')

    return redirect(url_for('app_detail', app_id=app_id))

@app.route('/admin/logs')
@admin_required
def admin_logs():
    """View system logs"""
    log_type = request.args.get('type', 'activity')
    page = request.args.get('page', 1, type=int)

    if log_type == 'login':
        logs = LoginLog.query.order_by(LoginLog.timestamp.desc()).paginate(
            page=page, per_page=50, error_out=False
        )
    elif log_type == 'download':
        logs = DownloadLog.query.order_by(DownloadLog.timestamp.desc()).paginate(
            page=page, per_page=50, error_out=False
        )
    elif log_type == 'admin':
        logs = AdminLog.query.order_by(AdminLog.timestamp.desc()).paginate(
            page=page, per_page=50, error_out=False
        )
    else:  # activity
        logs = ActivityLog.query.order_by(ActivityLog.timestamp.desc()).paginate(
            page=page, per_page=50, error_out=False
        )

    return render_template('admin/logs.html', logs=logs, log_type=log_type)

# Disabled public registration - redirect to login
@app.route('/register')
def register():
    """Public registration disabled - redirect to login"""
    flash('Account registration is disabled. Please contact an administrator to create an account.', 'info')
    return redirect(url_for('login'))

# Legacy admin login route (for backward compatibility)
@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """Legacy admin login - redirects to main login"""
    return redirect(url_for('login'))

@app.route('/admin')
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    total_apps = App.query.count()
    total_downloads = db.session.query(db.func.sum(App.downloads)).scalar() or 0
    recent_downloads = DownloadLog.query.order_by(DownloadLog.timestamp.desc()).limit(10).all()
    recent_apps = App.query.order_by(App.created_at.desc()).limit(5).all()
    total_users = User.query.count()

    return render_template('admin/dashboard.html',
                         total_apps=total_apps,
                         total_downloads=total_downloads,
                         recent_downloads=recent_downloads,
                         recent_apps=recent_apps,
                         total_users=total_users)

@app.route('/publisher')
@publisher_required
def publisher_dashboard():
    """Publisher dashboard"""
    user = get_current_user()
    user_apps = App.query.filter_by(user_id=user.id).all()
    total_user_downloads = sum(app.downloads for app in user_apps)

    return render_template('publisher/dashboard.html',
                         user_apps=user_apps,
                         total_user_downloads=total_user_downloads,
                         user=user)

@app.route('/admin/apps')
@admin_required
def admin_apps():
    """Admin apps management"""
    page = request.args.get('page', 1, type=int)
    apps = App.query.order_by(App.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('admin/apps.html', apps=apps)

@app.route('/admin/apps/add', methods=['GET', 'POST'])
@publisher_required
def admin_add_app():
    """Add new app"""
    if request.method == 'POST':
        try:
            # Get current user
            current_user = get_current_user()

            # Get form data and sanitize
            name = sanitize_input(request.form.get('name'))
            description = sanitize_input(request.form.get('description'))
            short_description = sanitize_input(request.form.get('short_description'))
            version = sanitize_input(request.form.get('version'))
            developer = sanitize_input(request.form.get('developer'))
            category = sanitize_input(request.form.get('category'))
            price = float(request.form.get('price', 0))
            is_featured = bool(request.form.get('is_featured')) and current_user.is_admin()  # Only admins can feature

            download_method = request.form.get('download_method')
            app_file_path = None
            external_url = None
            file_size = 0

            if download_method == 'url':
                # Handle external URL
                external_url = sanitize_input(request.form.get('external_url'))
                if not validate_url(external_url):
                    flash('Invalid external URL', 'error')
                    return render_template('admin/add_app.html')
            else:
                # Handle file upload
                app_file = request.files.get('app_file')
                if not app_file or not allowed_file(app_file.filename, 'app'):
                    flash('Please select a valid app file', 'error')
                    return render_template('admin/add_app.html')

                # Save app file
                filename = secure_filename(app_file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                app_file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'apps', unique_filename)
                app_file.save(app_file_path)

                # Get file size
                file_size = os.path.getsize(app_file_path)

            # Handle icon upload
            icon_path = None
            icon_file = request.files.get('icon')
            if icon_file and allowed_file(icon_file.filename, 'image'):
                icon_filename = secure_filename(icon_file.filename)
                unique_icon_filename = f"{uuid.uuid4()}_{icon_filename}"
                full_icon_path = os.path.join(app.config['UPLOAD_FOLDER'], 'screenshots', unique_icon_filename)
                icon_file.save(full_icon_path)

                # Resize icon
                with Image.open(full_icon_path) as img:
                    img.thumbnail((512, 512), Image.Resampling.LANCZOS)
                    img.save(full_icon_path)

                # Store relative path for database
                icon_path = f"screenshots/{unique_icon_filename}"

            # Create app record
            new_app = App(
                name=name,
                description=description,
                short_description=short_description,
                version=version,
                developer=developer,
                uploaded_by=current_user.username,
                category=category,
                price=price,
                file_path=app_file_path,
                external_url=external_url,
                file_size=file_size,
                icon_path=icon_path,
                is_featured=is_featured,
                user_id=current_user.id
            )

            db.session.add(new_app)
            db.session.commit()

            # Handle screenshots
            screenshots = request.files.getlist('screenshots')
            for i, screenshot in enumerate(screenshots):
                if screenshot and allowed_file(screenshot.filename, 'image'):
                    screenshot_filename = secure_filename(screenshot.filename)
                    unique_screenshot_filename = f"{uuid.uuid4()}_{screenshot_filename}"
                    full_screenshot_path = os.path.join(app.config['UPLOAD_FOLDER'], 'screenshots', unique_screenshot_filename)
                    screenshot.save(full_screenshot_path)

                    # Resize screenshot
                    with Image.open(full_screenshot_path) as img:
                        img.thumbnail((1920, 1080), Image.Resampling.LANCZOS)
                        img.save(full_screenshot_path)

                    # Store relative path for database
                    relative_screenshot_path = f"screenshots/{unique_screenshot_filename}"
                    screenshot_record = Screenshot(
                        app_id=new_app.id,
                        file_path=relative_screenshot_path,
                        order=i
                    )
                    db.session.add(screenshot_record)

            db.session.commit()

            log_admin_action('Add App', f'Added app: {name}')
            flash('App added successfully', 'success')
            return redirect(url_for('admin_apps'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Error adding app: {e}")
            flash('Error adding app', 'error')

    return render_template('admin/add_app.html')

@app.route('/admin/apps/edit/<int:app_id>', methods=['GET', 'POST'])
@admin_required
def admin_edit_app(app_id):
    """Edit existing app"""
    app_item = App.query.get_or_404(app_id)

    if request.method == 'POST':
        try:
            # Update app data
            app_item.name = sanitize_input(request.form.get('name'))
            app_item.description = sanitize_input(request.form.get('description'))
            app_item.short_description = sanitize_input(request.form.get('short_description'))
            app_item.version = sanitize_input(request.form.get('version'))
            app_item.developer = sanitize_input(request.form.get('developer'))
            app_item.category = sanitize_input(request.form.get('category'))
            app_item.price = float(request.form.get('price', 0))
            app_item.is_featured = bool(request.form.get('is_featured'))
            app_item.updated_at = datetime.utcnow()

            db.session.commit()

            log_admin_action('Edit App', f'Edited app: {app_item.name}')
            flash('App updated successfully', 'success')
            return redirect(url_for('admin_apps'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Error editing app: {e}")
            flash('Error updating app', 'error')

    return render_template('admin/edit_app.html', app=app_item)

@app.route('/admin/apps/delete/<int:app_id>', methods=['POST'])
@admin_required
def admin_delete_app(app_id):
    """Delete app"""
    app_item = App.query.get_or_404(app_id)

    try:
        # Delete files
        if app_item.file_path and os.path.exists(app_item.file_path):
            os.remove(app_item.file_path)

        if app_item.icon_path:
            full_icon_path = os.path.join(app.config['UPLOAD_FOLDER'], app_item.icon_path)
            if os.path.exists(full_icon_path):
                os.remove(full_icon_path)

        # Delete screenshots
        for screenshot in app_item.screenshots:
            full_screenshot_path = os.path.join(app.config['UPLOAD_FOLDER'], screenshot.file_path)
            if os.path.exists(full_screenshot_path):
                os.remove(full_screenshot_path)

        app_name = app_item.name
        db.session.delete(app_item)
        db.session.commit()

        log_admin_action('Delete App', f'Deleted app: {app_name}')
        flash('App deleted successfully', 'success')

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting app: {e}")
        flash('Error deleting app', 'error')

    return redirect(url_for('admin_apps'))

# Publisher routes (same as admin but different access)
@app.route('/publisher/apps/add', methods=['GET', 'POST'])
@publisher_required
def publisher_add_app():
    """Publisher add app - same as admin but different template"""
    return admin_add_app()  # Reuse the same logic

@app.route('/publisher/apps/edit/<int:app_id>', methods=['GET', 'POST'])
@publisher_required
def publisher_edit_app(app_id):
    """Publisher edit app"""
    current_user = get_current_user()
    app_item = App.query.get_or_404(app_id)

    # Publishers can only edit their own apps, admins can edit any
    if not current_user.is_admin() and app_item.user_id != current_user.id:
        flash('You can only edit your own apps', 'error')
        return redirect(url_for('publisher_dashboard'))

    if request.method == 'POST':
        try:
            # Update app data
            app_item.name = sanitize_input(request.form.get('name'))
            app_item.description = sanitize_input(request.form.get('description'))
            app_item.short_description = sanitize_input(request.form.get('short_description'))
            app_item.version = sanitize_input(request.form.get('version'))
            app_item.developer = sanitize_input(request.form.get('developer'))
            app_item.category = sanitize_input(request.form.get('category'))
            app_item.price = float(request.form.get('price', 0))
            # Only admins can change featured status
            if current_user.is_admin():
                app_item.is_featured = bool(request.form.get('is_featured'))
            app_item.updated_at = datetime.utcnow()

            db.session.commit()

            log_admin_action('Edit App', f'Edited app: {app_item.name}')
            flash('App updated successfully', 'success')
            return redirect(url_for('publisher_dashboard'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Error editing app: {e}")
            flash('Error updating app', 'error')

    return render_template('publisher/edit_app.html', app=app_item)

# Context processor for user session
@app.context_processor
def inject_user():
    return {
        'current_user': get_current_user() if session.get('user_id') else None
    }

# Custom template filters
@app.template_filter('nl2br')
def nl2br_filter(text):
    """Convert newlines to HTML line breaks"""
    if not text:
        return ""
    return text.replace('\n', '<br>\n')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
