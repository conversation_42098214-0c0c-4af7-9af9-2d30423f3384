{% extends "base.html" %}

{% block title %}System Logs - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-journal-text"></i> System Logs</h1>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <!-- Log Type Tabs -->
    <ul class="nav nav-tabs mb-4">
        <li class="nav-item">
            <a class="nav-link {% if log_type == 'activity' %}active{% endif %}" 
               href="{{ url_for('admin_logs', type='activity') }}">
                <i class="bi bi-activity"></i> User Activity
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {% if log_type == 'login' %}active{% endif %}" 
               href="{{ url_for('admin_logs', type='login') }}">
                <i class="bi bi-box-arrow-in-right"></i> Login Attempts
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {% if log_type == 'download' %}active{% endif %}" 
               href="{{ url_for('admin_logs', type='download') }}">
                <i class="bi bi-download"></i> Downloads
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {% if log_type == 'admin' %}active{% endif %}" 
               href="{{ url_for('admin_logs', type='admin') }}">
                <i class="bi bi-shield-check"></i> Admin Actions
            </a>
        </li>
    </ul>

    {% if logs.items %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            {% if log_type == 'activity' %}
                            <th>Time</th>
                            <th>User</th>
                            <th>Action</th>
                            <th>Details</th>
                            <th>IP Address</th>
                            {% elif log_type == 'login' %}
                            <th>Time</th>
                            <th>Username</th>
                            <th>Status</th>
                            <th>Reason</th>
                            <th>IP Address</th>
                            {% elif log_type == 'download' %}
                            <th>Time</th>
                            <th>App ID</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            {% elif log_type == 'admin' %}
                            <th>Time</th>
                            <th>Action</th>
                            <th>Details</th>
                            <th>IP Address</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs.items %}
                        <tr>
                            {% if log_type == 'activity' %}
                            <td>
                                <small>{{ log.timestamp.strftime('%m/%d/%Y %H:%M:%S') }}</small>
                            </td>
                            <td>
                                {% if log.user_id %}
                                <span class="badge bg-primary">{{ log.username }}</span>
                                {% else %}
                                <span class="badge bg-secondary">Anonymous</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.action == 'LOGIN' %}
                                <span class="badge bg-success">{{ log.action }}</span>
                                {% elif log.action == 'LOGOUT' %}
                                <span class="badge bg-warning">{{ log.action }}</span>
                                {% elif log.action == 'DOWNLOAD_APP' %}
                                <span class="badge bg-info">{{ log.action }}</span>
                                {% elif log.action == 'VIEW_APP' %}
                                <span class="badge bg-light text-dark">{{ log.action }}</span>
                                {% elif log.action == 'RATE_APP' %}
                                <span class="badge bg-warning">{{ log.action }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ log.action }}</span>
                                {% endif %}
                            </td>
                            <td><small>{{ log.details or '-' }}</small></td>
                            <td><small>{{ log.ip_address }}</small></td>
                            
                            {% elif log_type == 'login' %}
                            <td>
                                <small>{{ log.timestamp.strftime('%m/%d/%Y %H:%M:%S') }}</small>
                            </td>
                            <td>{{ log.username }}</td>
                            <td>
                                {% if log.success %}
                                <span class="badge bg-success">Success</span>
                                {% else %}
                                <span class="badge bg-danger">Failed</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if not log.success %}
                                <small class="text-danger">{{ log.failure_reason or 'Unknown' }}</small>
                                {% else %}
                                <small class="text-success">Login successful</small>
                                {% endif %}
                            </td>
                            <td><small>{{ log.ip_address }}</small></td>
                            
                            {% elif log_type == 'download' %}
                            <td>
                                <small>{{ log.timestamp.strftime('%m/%d/%Y %H:%M:%S') }}</small>
                            </td>
                            <td>
                                <a href="{{ url_for('app_detail', app_id=log.app_id) }}" class="text-decoration-none">
                                    App #{{ log.app_id }}
                                </a>
                            </td>
                            <td><small>{{ log.ip_address }}</small></td>
                            <td><small>{{ log.user_agent[:50] }}{% if log.user_agent|length > 50 %}...{% endif %}</small></td>
                            
                            {% elif log_type == 'admin' %}
                            <td>
                                <small>{{ log.timestamp.strftime('%m/%d/%Y %H:%M:%S') }}</small>
                            </td>
                            <td>
                                <span class="badge bg-warning text-dark">{{ log.action }}</span>
                            </td>
                            <td><small>{{ log.details or '-' }}</small></td>
                            <td><small>{{ log.ip_address }}</small></td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if logs.pages > 1 %}
    <nav aria-label="Log pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if logs.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin_logs', type=log_type, page=logs.prev_num) }}">Previous</a>
            </li>
            {% endif %}
            
            {% for page_num in logs.iter_pages() %}
                {% if page_num %}
                    {% if page_num != logs.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_logs', type=log_type, page=page_num) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if logs.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin_logs', type=log_type, page=logs.next_num) }}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-journal-x display-1 text-muted"></i>
            <h3 class="mt-3">No Logs Found</h3>
            <p class="text-muted">No {{ log_type }} logs available yet.</p>
        </div>
    </div>
    {% endif %}

    <!-- Log Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ logs.total }}</h4>
                    <small>Total {{ log_type.title() }} Logs</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ logs.pages }}</h4>
                    <small>Total Pages</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ logs.per_page }}</h4>
                    <small>Logs Per Page</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h4>{{ logs.page }}</h4>
                    <small>Current Page</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
